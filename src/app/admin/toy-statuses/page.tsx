'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import AdminTabs from '../../../components/admin/AdminTabs';

interface ToyStatus {
  name: string;
  label: string;
  count: number;
}

export default function AdminToyStatuses() {
  const { user } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [toyStatuses, setToyStatuses] = useState<ToyStatus[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [newToyStatus, setNewToyStatus] = useState('');
  const [newToyStatusLabel, setNewToyStatusLabel] = useState('');
  const [editingStatus, setEditingStatus] = useState<string | null>(null);
  const [editingLabel, setEditingLabel] = useState('');

  // Presmerovanie na domovskú stránku, ak používateľ nie je admin
  useEffect(() => {
    if (user && user.role !== 'admin') {
      router.push('/');
    } else if (!user && !isLoading) {
      router.push('/');
    }
  }, [user, router, isLoading]);

  // Načítanie statusov hračiek
  useEffect(() => {
    async function loadToyStatuses() {
      if (!user || user.role !== 'admin') {
        setIsLoading(false);
        return;
      }

      try {
        // Získanie Firebase tokenu pre autentifikáciu
        if (!user.getIdToken) return;
        const token = await user.getIdToken();

        // Kontrola, či má používateľ dbUserId
        if (!user.dbUserId) {
          throw new Error('Chýba ID používateľa');
        }

        const response = await fetch('/api/toy-statuses', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Nepodarilo sa načítať statusy hračiek');
        }

        const data = await response.json();
        setToyStatuses(data);
      } catch (err) {
        setError('Nastala chyba pri načítaní statusov hračiek');
        console.error('Chyba pri načítaní statusov hračiek:', err);
      } finally {
        setIsLoading(false);
      }
    }

    if (user) {
      loadToyStatuses();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  // Funkcia pre pridanie nového statusu hračky
  const handleAddToyStatus = async () => {
    if (!newToyStatus.trim()) {
      setError('Zadajte názov statusu hračky');
      return;
    }

    setIsAdding(true);
    setError(null);
    setSuccess(null);

    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      // Kontrola, či má používateľ dbUserId
      if (!user.dbUserId) {
        throw new Error('Chýba ID používateľa');
      }

      const response = await fetch('/api/toy-statuses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: newToyStatus.toUpperCase(),
          label: newToyStatusLabel || newToyStatus // Use label if provided, otherwise use the name
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa pridať status hračky');
      }

      const newStatus = await response.json();
      setToyStatuses([...toyStatuses, newStatus]);
      setNewToyStatus('');
      setNewToyStatusLabel('');
      setSuccess('Status hračky bol úspešne pridaný');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('Chyba pri pridávaní statusu hračky:', err);
      setError(err instanceof Error ? err.message : 'Nastala chyba pri pridávaní statusu hračky');
    } finally {
      setIsAdding(false);
    }
  };

  // Funkcia pre odstránenie statusu hračky
  const handleDeleteToyStatus = async (statusName: string) => {
    if (!confirm('Naozaj chcete odstrániť tento status hračky?')) {
      return;
    }

    setIsDeleting(true);
    setError(null);
    setSuccess(null);

    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      // Kontrola, či má používateľ dbUserId
      if (!user.dbUserId) {
        throw new Error('Chýba ID používateľa');
      }

      const response = await fetch(`/api/toy-statuses/${encodeURIComponent(statusName)}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa odstrániť status hračky');
      }

      setToyStatuses(toyStatuses.filter(status => status.name !== statusName));
      setSuccess('Status hračky bol úspešne odstránený');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('Chyba pri odstraňovaní statusu hračky:', err);
      setError(err instanceof Error ? err.message : 'Nastala chyba pri odstraňovaní statusu hračky');
    } finally {
      setIsDeleting(false);
    }
  };

  // Funkcia pre začatie úpravy labelu
  const startEditing = (status: ToyStatus) => {
    setEditingStatus(status.name);
    setEditingLabel(status.label);
  };

  // Funkcia pre zrušenie úpravy
  const cancelEditing = () => {
    setEditingStatus(null);
    setEditingLabel('');
  };

  // Funkcia pre uloženie upraveného labelu
  const saveLabel = async (statusName: string) => {
    setIsUpdating(true);
    setError(null);
    setSuccess(null);

    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba hashedUserId používateľa');
      }

      const response = await fetch(`/api/toy-statuses/${encodeURIComponent(statusName)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          label: editingLabel
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa aktualizovať label statusu hračky');
      }

      // Aktualizácia statusu v zozname
      setToyStatuses(toyStatuses.map(status =>
        status.name === statusName
          ? { ...status, label: editingLabel }
          : status
      ));

      setEditingStatus(null);
      setEditingLabel('');
      setSuccess('Label statusu hračky bol úspešne aktualizovaný');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('Chyba pri aktualizácii labelu statusu hračky:', err);
      setError(err instanceof Error ? err.message : 'Nastala chyba pri aktualizácii labelu statusu hračky');
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam administrátorský panel...</p>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return null; // Toto sa nezobrazí, pretože useEffect presmeruje používateľa
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-16">
      <h1 className="text-3xl font-bold mb-4">Administrátorský panel</h1>

      <AdminTabs />

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-24">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Správa statusov hračiek</h2>

          {/* Formulár pre pridanie nového statusu */}
          <div className="mb-6 p-4 bg-neutral-50 rounded-lg">
            <h3 className="text-lg font-medium mb-3">Pridať nový status hračky</h3>
            <div className="flex flex-col sm:flex-row gap-3">
              <input
                type="text"
                placeholder="Názov statusu (napr. AVAILABLE)"
                value={newToyStatus}
                onChange={(e) => setNewToyStatus(e.target.value)}
                className="flex-1 px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                disabled={isAdding}
              />
              <input
                type="text"
                placeholder="Slovenský preklad (napr. Dostupná)"
                value={newToyStatusLabel}
                onChange={(e) => setNewToyStatusLabel(e.target.value)}
                className="flex-1 px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                disabled={isAdding}
              />
              <button
                onClick={handleAddToyStatus}
                className="btn btn-primary whitespace-nowrap"
                disabled={isAdding || !newToyStatus.trim()}
              >
                {isAdding ? 'Pridávam...' : 'Pridať status'}
              </button>
            </div>
          </div>

          {toyStatuses.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-neutral-200">
                <thead className="bg-neutral-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Názov
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Slovenský preklad
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Počet hračiek
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Akcie
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-neutral-200">
                  {toyStatuses.map((status) => (
                    <tr key={status.name}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-900">
                        {status.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        {editingStatus === status.name ? (
                          <div className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={editingLabel}
                              onChange={(e) => setEditingLabel(e.target.value)}
                              className="px-2 py-1 border border-neutral-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary"
                              disabled={isUpdating}
                            />
                            <button
                              onClick={() => saveLabel(status.name)}
                              className="text-green-600 hover:text-green-800 text-sm"
                              disabled={isUpdating}
                            >
                              {isUpdating ? '...' : '✓'}
                            </button>
                            <button
                              onClick={cancelEditing}
                              className="text-red-600 hover:text-red-800 text-sm"
                              disabled={isUpdating}
                            >
                              ✗
                            </button>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between">
                            <span>{status.label}</span>
                            <button
                              onClick={() => startEditing(status)}
                              className="ml-2 text-primary hover:text-primary-dark text-sm"
                            >
                              ✏️
                            </button>
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        {status.count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        <button
                          onClick={() => handleDeleteToyStatus(status.name)}
                          className="text-red-600 hover:text-red-800"
                          disabled={isDeleting || status.count > 0}
                          title={status.count > 0 ? 'Nie je možné odstrániť status, ktorý je používaný' : 'Odstrániť status'}
                        >
                          🗑️
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-neutral-500">Žiadne statusy hračiek neboli nájdené.</p>
          )}
        </div>
      </div>
    </div>
  );
}
